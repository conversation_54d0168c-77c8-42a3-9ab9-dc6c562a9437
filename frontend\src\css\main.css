/* Toggle Button */
#assistant-toggle-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 100000;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: none;
    background-color: #007A3E;
    color: #fff;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  #assistant-toggle-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0,0,0,0.2);
  }

  /* Panel */
  #outlook-assistant-panel {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 300px;
    background-color: white;
    border: 1px solid #e5e5e5;
    border-radius: 12px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    z-index: 99999;
    font-family: 'Segoe UI', system-ui, sans-serif;
    display: none;
    flex-direction: column;
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  /* Panel Header */
  .panel-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
  }

  .panel-logo {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #007A3E;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
  }

  .panel-title {
    font-weight: 600;
    font-size: 15px;
  }

  /* Buttons */
  .assistant-btn {
    border: none;
    border-radius: 6px;
    padding: 10px 14px;
    margin-bottom: 8px;
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    text-align: left;
    transition: all 0.2s ease;
    background-color: #f5f5f5;
    color: #333;
  }

  .assistant-btn:hover {
    background-color: #e9e9e9;
    transform: translateY(-1px);
  }

  .assistant-btn-primary {
    background-color: #007A3E;
    color: white;
  }

  .assistant-btn-primary:hover {
    background-color: rgb(3, 78, 41);
  }

  /* Sections */
  .section-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin: 12px 0 8px 0;
    display: flex;
    align-items: center;
    gap: 6px;
  }


/* Chatbot *//* Chatbot Container */
#chatbot-container {
  position: fixed;
  bottom: 80px;
  right: 20px;
  width: 350px;
  height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  z-index: 99999;
  overflow: hidden;
  border: 1px solid #e5e5e5;
  font-family: 'Segoe UI', system-ui, sans-serif;
}

#chatbot-container.hidden {
  display: none;
}

/* Chatbot Header */
.chatbot-header {
  padding: 12px 16px;
  background: #007A3E;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chatbot-title {
  font-weight: 600;
  font-size: 15px;
}

/* Close Button */
.chatbot-close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chatbot-close-btn:hover {
  opacity: 0.8;
}

/* Chat Area */
.chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background: #f9fafb;
}

/* Message Bubble */
.message {
  margin-bottom: 12px;
  max-width: 80%;
  padding: 10px 14px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
}

.user-message {
  background: #007A3E;
  color: white;
  margin-left: auto;
  border-bottom-right-radius: 4px;
}

.bot-message {
  background: white;
  color: #1f2937;
  border: 1px solid #e5e7eb;
  margin-right: auto;
  border-bottom-left-radius: 4px;
}

/* Chat Input */
.chat-input-container {
  display: flex;
  padding: 12px;
  border-top: 1px solid #e5e7eb;
  background: white;
}

#chat-input {
  flex: 1;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 10px 12px;
  font-size: 14px;
  resize: none;
  max-height: 120px;
  outline: none;
}

#chat-input:focus {
  border-color: #007A3E;
}

/* Send Button */
.send-btn {
  background: #007A3E;
  color: white;
  border: none;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  margin-left: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn:hover {
  background: rgb(3, 78, 41);
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  padding: 8px 12px;
}

.typing-indicator span {
  height: 8px;
  width: 8px;
  background: #9ca3af;
  border-radius: 50%;
  display: inline-block;
  margin: 0 2px;
  animation: bounce 1.5s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-5px);
  }
}